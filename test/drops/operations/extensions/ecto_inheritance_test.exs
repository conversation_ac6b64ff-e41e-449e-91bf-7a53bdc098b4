defmodule Drops.Operations.Extensions.EctoInheritanceTest do
  use ExUnit.Case, async: false

  setup_all do
    # Ensure test schemas are available
    Code.ensure_loaded(Test.Ecto.TestSchemas.UserSchema)
    :ok
  end

  setup do
    # Set up database ownership for tests
    :ok = Ecto.Adapters.SQL.Sandbox.checkout(Drops.TestRepo)

    # Ensure the table exists
    Ecto.Adapters.SQL.query!(Drops.TestRepo, """
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        email TEXT,
        admin BOOLEAN DEFAULT FALSE,
        age INTEGER,
        score INTEGER,
        inserted_at DATETIME,
        updated_at DATETIME
      )
    """)

    # Clean up any existing data
    Drops.TestRepo.delete_all(Test.Ecto.TestSchemas.UserSchema)
    :ok
  end

  describe "simple inheritance case" do
    defmodule SimpleBase do
      use Drops.Operations, type: :command, repo: Drops.TestRepo
    end

    defmodule SimpleCommand do
      use SimpleBase

      schema(Test.Ecto.TestSchemas.UserSchema)

      steps do
        @impl true
        def execute(%{changeset: changeset}) do
          insert(changeset)
        end

        @impl true
        def get_struct(_context) do
          struct(ecto_schema())
        end
      end
    end

    test "simple inheritance works without AST conflicts" do
      assert {:ok, user} =
               SimpleCommand.call(%{
                 params: %{name: "Jane", email: "<EMAIL>"}
               })

      assert user.name == "Jane"
      assert user.email == "<EMAIL>"
    end
  end

  describe "multi-level inheritance case" do
    defmodule Commands do
      use Drops.Operations, type: :command, repo: Drops.TestRepo
    end

    defmodule Commands.Save do
      use Commands

      steps do
        @impl true
        def execute(%{changeset: changeset}) do
          insert(changeset)
        end

        # This get_struct/1 should be grouped with the extension's get_struct/1
        @impl true
        def get_struct(%{id: id}) do
          repo().get!(ecto_schema(), id)
        end
      end
    end

    defmodule Users.Save do
      use Commands.Save

      schema(Test.Ecto.TestSchemas.UserSchema)
    end

    test "multi-level inheritance works without AST conflicts" do
      assert {:ok, user} =
               Users.Save.call(%{
                 params: %{name: "Jane", email: "<EMAIL>"}
               })

      assert user.name == "Jane"
      assert user.email == "<EMAIL>"
    end
  end
end
